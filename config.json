{"checkIntervalSeconds": 1800, "notifications": [{"id": "wechat", "enabled": false, "type": "generic_webhook", "webhookUrlEnvVar": "WEWOR<PERSON>_BOT_KEY_CHANGES", "triggerOn": ["added", "removed"], "requestBodyTemplate": {"msgtype": "markdown", "markdown": {"content": "**模型监控状态更新**\n> 服务商: {providerName} (`{providerId}`)\n\n**错误**:\n{errorDetails}\n**新增模型:**\n{addedMarkdownList}\n\n**移除模型:**\n{removedMarkdownList}"}}}, {"id": "lark", "enabled": true, "type": "generic_webhook", "webhookUrlEnvVar": "LARK_BOT_WEBHOOK_URL", "triggerOn": ["added", "removed", "error"], "requestBodyTemplate": {"msg_type": "interactive", "card": {"config": {"wide_screen_mode": true}, "header": {"template": "wathet", "title": {"content": "🔔 ModelSentry 状态更新", "tag": "plain_text"}}, "elements": [{"tag": "div", "text": {"content": "模型变动<at id=all></at>", "tag": "lark_md"}}, {"tag": "img", "img_key": "img_v3_02ls_fcae9cd2-3bc1-468a-b910-00585e1bc9hu", "alt": {"tag": "plain_text", "content": ""}, "mode": "crop_center", "preview": true, "compact_width": false}, {"tag": "hr"}, {"tag": "markdown", "content": "**模型提供商**: {providerName}\n\n❌ **错误信息**：\n{errorDetails}\n\n➕ **新增模型**：\n{addedMarkdownList}\n\n➖ **移除模型**：\n{removedMarkdownList}"}, {"tag": "hr"}, {"tag": "action", "actions": [{"tag": "button", "text": {"tag": "plain_text", "content": "查看详情"}, "type": "primary", "multi_url": {"url": "https://model-watch.oi-oi.de", "android_url": "", "ios_url": "", "pc_url": ""}}]}]}}}], "frontendSettings": {"title": "ModelSentry - AI 模型监控", "faviconUrl": "/static/modelsentry.svg", "backgroundImageUrl": "/static/background.jpg", "backgroundOpacity": 0.7, "modelCopySeparator": ","}, "providers": [{"id": "gemini", "name": "Google Gemini", "enabled": true, "url": "https://generativelanguage.googleapis.com/v1beta/models", "method": "GET", "auth": {"type": "query", "keyParamName": "key", "apiKeyEnvVar": "GEMINI_API_KEY"}, "parsing": {"modelListPath": "models", "modelNamePath": "name", "modelNameRegex": "^models/(.+)$"}}, {"id": "openai", "name": "Openai", "enabled": true, "url": "https://api.openai.com/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "OPENAI_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "claude", "name": "Anthropic <PERSON>", "enabled": true, "url": "https://api.anthropic.com/v1/models", "method": "GET", "auth": {"type": "custom_headers", "apiKeyEnvVar": "CLAUDE_API_KEY", "customHeaders": {"x-api-key": {"envVar": "CLAUDE_API_KEY"}, "anthropic-version": {"value": "2023-06-01"}}}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "groq", "name": "GroqCloud", "enabled": true, "url": "https://api.groq.com/openai/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "GROQ_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "cerebras", "name": "Cerebras", "enabled": true, "url": "https://api.cerebras.ai/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "CEREBRAS_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "cohere", "name": "Cohere", "enabled": true, "url": "https://api.cohere.com/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "bearer ", "apiKeyEnvVar": "COHERE_API_KEY"}, "parsing": {"modelListPath": "models", "modelNamePath": "name"}}, {"id": "xai", "name": "XAI", "enabled": true, "url": "https://api.x.ai/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "XAI_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "mistral", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "url": "https://api.mistral.ai/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "MISTRAL_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "sambanova", "name": "Sambanova AI", "enabled": true, "url": "https://api.sambanova.ai/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "SAMBANOVA_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "together", "name": "Together AI", "enabled": true, "url": "https://api.together.xyz/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "TOGETHER_API_KEY"}, "parsing": {"modelNamePath": "id"}}, {"id": "360", "name": "360 AI", "enabled": true, "url": "https://api.360.cn/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "CN_360_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "siliconflow", "name": "Siliconflow", "enabled": true, "url": "https://api.siliconflow.cn/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "SILICONFLOW_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "baichuan", "name": "BaiChuan", "enabled": true, "url": "https://api.baichuan-ai.com/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "BAICHUAN_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "model"}}, {"id": "deepseek", "name": "DeepSeek", "enabled": true, "url": "https://api.deepseek.com/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "DEEPSEEK_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}, {"id": "<PERSON>fun", "name": "<PERSON><PERSON><PERSON>", "enabled": true, "url": "https://api.stepfun.com/v1/models", "method": "GET", "auth": {"type": "header", "headerName": "Authorization", "valuePrefix": "Bearer ", "apiKeyEnvVar": "STEPFUN_API_KEY"}, "parsing": {"modelListPath": "data", "modelNamePath": "id"}}]}