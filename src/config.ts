export interface AuthConfig {
  type: 'header' | 'query' | 'custom_headers';
  headerName?: string;
  valuePrefix?: string;
  keyParamName?: string;
  apiKeyEnvVar?: string;
  customHeaders?: Record<string, { envVar?: string; value?: string }>;
}

export interface ParsingConfig {
  modelListPath?: string;
  modelNamePath: string;
  modelNameRegex?: string;
}

export interface IconConfig {
  slug?: string;
  format?: 'svg' | 'png' | 'webp';
  theme?: 'light' | 'dark';
  size?: number;
}

export interface ProviderConfig {
  id: string;
  name: string;
  enabled: boolean;
  url: string;
  method: string;
  auth: AuthConfig;
  parsing: ParsingConfig;
  icon?: IconConfig;
}

export interface NotificationConfig {
  id: string;
  enabled: boolean;
  type: string;
  webhookUrlEnvVar: string;
  triggerOn: string[];
  requestBodyTemplate: any;
}

export interface GlobalIconSettings {
  enabled: boolean;
  cdnSource: 'unpkg' | 'npmmirror';
  format: 'svg' | 'png' | 'webp';
  theme: 'light' | 'dark';
  size: number;
  fallbackIcon: string;
}

export interface FrontendSettings {
  title: string;
  faviconUrl?: string;
  backgroundImageUrl?: string;
  backgroundOpacity?: number;
  modelCopySeparator?: string;
}

export interface Config {
  checkIntervalSeconds: number;
  notifications: NotificationConfig[];
  frontendSettings: FrontendSettings;
  iconSettings?: GlobalIconSettings;
  providers: ProviderConfig[];
}

export async function loadConfig(): Promise<Config> {
  const configFile = Bun.file('config.json');
  const configText = await configFile.text();
  const config = JSON.parse(configText) as Config;

  // Validate required fields
  if (!config.checkIntervalSeconds || config.checkIntervalSeconds < 60) {
    throw new Error('checkIntervalSeconds must be at least 60 seconds');
  }

  if (!config.providers || config.providers.length === 0) {
    throw new Error('At least one provider must be configured');
  }

  // Validate and auto-disable providers with missing environment variables
  config.providers = validateAndFilterProviders(config.providers);

  // Validate and auto-disable notifications with missing environment variables
  config.notifications = validateAndFilterNotifications(config.notifications);

  // Set defaults
  config.frontendSettings = {
    ...{
      title: 'ModelSentry - AI 模型监控',
      faviconUrl: '/static/favicon.ico',
      backgroundImageUrl: '/static/background.jpg',
      backgroundOpacity: 0.7,
      modelCopySeparator: ','
    },
    ...config.frontendSettings
  };

  config.iconSettings = {
    ...{
      enabled: true,
      cdnSource: 'unpkg' as const,
      format: 'svg' as const,
      theme: 'light' as const,
      size: 32,
      fallbackIcon: 'ai'
    },
    ...config.iconSettings
  };

  return config;
}

export function getEnvVar(envVarName: string): string | undefined {
  return process.env[envVarName];
}

function validateAndFilterProviders(providers: ProviderConfig[]): ProviderConfig[] {
  const validatedProviders: ProviderConfig[] = [];

  for (const provider of providers) {
    if (!provider.enabled) {
      validatedProviders.push(provider);
      continue;
    }

    const hasValidEnvVars = validateProviderEnvironmentVariables(provider);

    if (!hasValidEnvVars) {
      console.warn(`⚠️  Provider '${provider.name}' (${provider.id}) is enabled but missing required environment variables. Auto-disabling.`, {
        providerId: provider.id,
        requiredEnvVars: getRequiredEnvVarsForProvider(provider)
      });

      // Auto-disable the provider
      validatedProviders.push({
        ...provider,
        enabled: false
      });
    } else {
      validatedProviders.push(provider);
    }
  }

  return validatedProviders;
}

function validateAndFilterNotifications(notifications: NotificationConfig[]): NotificationConfig[] {
  const validatedNotifications: NotificationConfig[] = [];

  for (const notification of notifications) {
    if (!notification.enabled) {
      validatedNotifications.push(notification);
      continue;
    }

    const webhookUrl = getEnvVar(notification.webhookUrlEnvVar);

    if (!webhookUrl || webhookUrl.trim().length === 0) {
      console.warn(`⚠️  Notification '${notification.id}' is enabled but missing webhook URL environment variable '${notification.webhookUrlEnvVar}'. Auto-disabling.`, {
        notificationId: notification.id,
        envVar: notification.webhookUrlEnvVar
      });

      // Auto-disable the notification
      validatedNotifications.push({
        ...notification,
        enabled: false
      });
    } else {
      validatedNotifications.push(notification);
    }
  }

  return validatedNotifications;
}

function validateProviderEnvironmentVariables(provider: ProviderConfig): boolean {
  if (provider.auth.type === 'header' || provider.auth.type === 'query') {
    const apiKey = getEnvVar(provider.auth.apiKeyEnvVar || '');
    return !!apiKey && apiKey.trim().length > 0;
  } else if (provider.auth.type === 'custom_headers' && provider.auth.customHeaders) {
    // For custom headers, check if at least one required header has a value
    for (const [headerName, headerConfig] of Object.entries(provider.auth.customHeaders)) {
      if (headerConfig.envVar) {
        const value = getEnvVar(headerConfig.envVar);
        if (value && value.trim().length > 0) {
          return true;
        }
      } else if (headerConfig.value && headerConfig.value.trim().length > 0) {
        return true;
      }
    }
    return false;
  }
  return true; // For unknown auth types, assume valid
}

function getRequiredEnvVarsForProvider(provider: ProviderConfig): string[] {
  const envVars: string[] = [];

  if (provider.auth.type === 'header' || provider.auth.type === 'query') {
    if (provider.auth.apiKeyEnvVar) {
      envVars.push(provider.auth.apiKeyEnvVar);
    }
  } else if (provider.auth.type === 'custom_headers' && provider.auth.customHeaders) {
    for (const [headerName, headerConfig] of Object.entries(provider.auth.customHeaders)) {
      if (headerConfig.envVar) {
        envVars.push(headerConfig.envVar);
      }
    }
  }

  return envVars;
}
