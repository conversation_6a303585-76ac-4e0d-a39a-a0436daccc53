import { ProviderConfig, getEnvVar } from './config.ts';
import { log } from './logger.ts';

export interface ModelData {
  name: string;
  id: string;
}

export interface ProviderStatus {
  id: string;
  name: string;
  enabled: boolean;
  lastCheck: Date | null;
  lastSuccess: Date | null;
  models: ModelData[];
  error: string | null;
  modelCount: number;
  retryCount: number;
  nextRetryAt: Date | null;
  isRetrying: boolean;
}

export class ProviderManager {
  private providers: Map<string, ProviderStatus> = new Map();
  private readonly MAX_RETRY_COUNT = 2;
  private readonly RETRY_INTERVAL_MS = 2 * 60 * 1000; // 2 minutes

  constructor(private configs: ProviderConfig[]) {
    this.initializeProviders();

    const enabledCount = Array.from(this.providers.values()).filter(p => p.enabled).length;
    log.info('Provider manager initialized', {
      total: configs.length,
      enabled: enabledCount,
      disabled: configs.length - enabledCount
    });
  }

  private initializeProviders() {
    for (const config of this.configs) {
      // Config validation already handled environment variables during loading
      // So we can trust the enabled status here
      this.providers.set(config.id, {
        id: config.id,
        name: config.name,
        enabled: config.enabled,
        lastCheck: null,
        lastSuccess: null,
        models: [],
        error: null,
        modelCount: 0,
        retryCount: 0,
        nextRetryAt: null,
        isRetrying: false
      });
    }
  }



  async fetchModels(providerId: string): Promise<ModelData[]> {
    const config = this.configs.find(c => c.id === providerId);
    const status = this.providers.get(providerId);

    if (!config || !status || !config.enabled) {
      throw new Error(`Provider ${providerId} not found or disabled`);
    }

    // Check if we should skip due to retry cooldown
    if (status.nextRetryAt && new Date() < status.nextRetryAt) {
      const remainingTime = Math.ceil((status.nextRetryAt.getTime() - Date.now()) / 1000);
      log.debug('Skipping fetch - retry cooldown active', {
        providerId,
        remainingTime: `${remainingTime}s`,
        retryCount: status.retryCount
      });
      throw new Error(`Retry cooldown active, next retry in ${remainingTime}s`);
    }

    return await this.attemptFetchWithRetry(providerId, config, status);
  }

  private async attemptFetchWithRetry(providerId: string, config: ProviderConfig, status: ProviderStatus): Promise<ModelData[]> {
    const maxAttempts = this.MAX_RETRY_COUNT + 1; // Initial attempt + retries
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        status.lastCheck = new Date();

        if (attempt > 1) {
          status.isRetrying = true;
          log.info('Retrying model fetch', {
            providerId,
            attempt,
            maxAttempts,
            retryCount: status.retryCount
          });
        }

        const models = await this.performFetch(providerId, config);

        // Success - reset retry state
        status.error = null;
        status.retryCount = 0;
        status.nextRetryAt = null;
        status.isRetrying = false;
        status.models = models;
        status.modelCount = models.length;
        status.lastSuccess = new Date();

        log.debug('Models fetched successfully', {
          providerId,
          modelCount: models.length,
          attempt,
          durationMs: Date.now() - status.lastCheck.getTime()
        });

        return models;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt < maxAttempts) {
          // Schedule next retry
          status.retryCount = attempt;
          status.nextRetryAt = new Date(Date.now() + this.RETRY_INTERVAL_MS);
          status.isRetrying = false;

          log.warn('Model fetch failed, scheduling retry', {
            providerId,
            attempt,
            maxAttempts,
            nextRetryAt: status.nextRetryAt.toISOString(),
            error: lastError.message
          });

          // Don't throw yet, continue to next attempt
          continue;
        } else {
          // All attempts failed
          status.error = lastError.message;
          status.retryCount = this.MAX_RETRY_COUNT;
          status.nextRetryAt = null;
          status.isRetrying = false;

          log.error('All retry attempts failed', lastError.message, {
            providerId,
            totalAttempts: maxAttempts,
            finalError: lastError.message
          });

          throw lastError;
        }
      }
    }

    // This should never be reached, but just in case
    throw lastError || new Error('Unknown error during fetch attempts');
  }

  private async performFetch(providerId: string, config: ProviderConfig): Promise<ModelData[]> {
    const headers = this.buildHeaders(config);
    const url = this.buildUrl(config);

    log.request('Fetching models', {
      providerId,
      url: url.replace(/key=[^&]+/g, 'key=***'),
      method: config.method
    });

    const response = await fetch(url, {
      method: config.method,
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return this.parseModels(data, config);
  }

  private buildHeaders(config: ProviderConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'ModelSentry/1.0'
    };

    if (config.auth.type === 'header' && config.auth.headerName && config.auth.apiKeyEnvVar) {
      const apiKey = getEnvVar(config.auth.apiKeyEnvVar);
      if (apiKey) {
        const prefix = config.auth.valuePrefix || '';
        headers[config.auth.headerName] = prefix + apiKey;
      }
    } else if (config.auth.type === 'custom_headers' && config.auth.customHeaders) {
      for (const [headerName, headerConfig] of Object.entries(config.auth.customHeaders)) {
        if (headerConfig.envVar) {
          const value = getEnvVar(headerConfig.envVar);
          if (value) headers[headerName] = value;
        } else if (headerConfig.value) {
          headers[headerName] = headerConfig.value;
        }
      }
    }

    return headers;
  }

  private buildUrl(config: ProviderConfig): string {
    let url = config.url;
    
    if (config.auth.type === 'query' && config.auth.keyParamName && config.auth.apiKeyEnvVar) {
      const apiKey = getEnvVar(config.auth.apiKeyEnvVar);
      if (apiKey) {
        const separator = url.includes('?') ? '&' : '?';
        url += `${separator}${config.auth.keyParamName}=${encodeURIComponent(apiKey)}`;
      }
    }
    
    return url;
  }

  private parseModels(data: any, config: ProviderConfig): ModelData[] {
    try {
      let modelList = data;
      
      if (config.parsing.modelListPath) {
        const pathParts = config.parsing.modelListPath.split('.');
        for (const part of pathParts) {
          modelList = modelList?.[part];
        }
      }

      if (!Array.isArray(modelList)) {
        throw new Error('Model list is not an array');
      }

      return modelList.map((item: any) => {
        const pathParts = config.parsing.modelNamePath.split('.');
        let modelName = item;
        
        for (const part of pathParts) {
          modelName = modelName?.[part];
        }

        if (typeof modelName !== 'string') {
          throw new Error(`Model name is not a string: ${JSON.stringify(modelName)}`);
        }

        if (config.parsing.modelNameRegex) {
          const match = modelName.match(new RegExp(config.parsing.modelNameRegex));
          if (match && match[1]) {
            modelName = match[1];
          }
        }

        return {
          name: modelName,
          id: modelName
        };
      });
    } catch (error) {
      throw new Error(`Failed to parse models: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  getProviderStatus(providerId: string): ProviderStatus | undefined {
    return this.providers.get(providerId);
  }

  getAllProviderStatuses(): ProviderStatus[] {
    return Array.from(this.providers.values());
  }

  getEnabledProviders(): ProviderConfig[] {
    return this.configs.filter(config => {
      const status = this.providers.get(config.id);
      return status?.enabled || false;
    });
  }
}
