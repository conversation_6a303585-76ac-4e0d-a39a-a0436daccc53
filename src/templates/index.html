<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <link rel="icon" href="{{faviconUrl}}" type="image/x-icon">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        /* 服务端注入的CSS变量 */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            background: var(--backgroundCss);
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            min-height: 100vh;
            color: #2d3748;
            line-height: 1.7;
            font-weight: 400;
            font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
            font-variant-numeric: tabular-nums;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .overlay {
            background: linear-gradient(135deg, rgba(255, 255, 255, var(--backgroundOpacity)) 0%, rgba(248, 250, 252, var(--backgroundOpacity)) 100%);
            min-height: 100vh;
            backdrop-filter: blur(15px) saturate(180%);
            position: relative;
        }
        .overlay::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 0和1数字装饰 */
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 120 120'%3E%3Ctext x='20' y='30' font-family='monospace' font-size='16' fill='%23667eea' opacity='0.15'%3E0%3C/text%3E%3Ctext x='80' y='60' font-family='monospace' font-size='14' fill='%23764ba2' opacity='0.12'%3E1%3C/text%3E%3Ctext x='40' y='90' font-family='monospace' font-size='12' fill='%23f093fb' opacity='0.1'%3E0%3C/text%3E%3Ctext x='10' y='110' font-family='monospace' font-size='10' fill='%23ff6b6b' opacity='0.08'%3E1%3C/text%3E%3Ctext x='90' y='25' font-family='monospace' font-size='11' fill='%23667eea' opacity='0.09'%3E1%3C/text%3E%3Ctext x='60' y='45' font-family='monospace' font-size='13' fill='%23764ba2' opacity='0.11'%3E0%3C/text%3E%3C/svg%3E"),
                /* 简洁的点阵图案 */
                radial-gradient(circle at center, rgba(102, 126, 234, 0.06) 1px, transparent 1px);
            background-size: 120px 120px, 40px 40px;
            background-position: 0 0, 20px 20px;
            pointer-events: none;
            z-index: -1;
            opacity: 0.4;
            animation: binaryFloat 25s linear infinite;
        }

        @keyframes binaryFloat {
            0% {
                transform: translateX(0) translateY(0);
            }
            100% {
                transform: translateX(-120px) translateY(-120px);
            }
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2.5rem;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #1a2980 0%, #26d0ce 100%);
            padding: 2rem;
            border-radius: 24px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            transform: translateY(0);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .header::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 极简光晕装饰 */
                radial-gradient(ellipse at 30% 30%, rgba(30, 64, 175, 0.03) 0%, transparent 70%),
                radial-gradient(ellipse at 70% 70%, rgba(55, 48, 163, 0.03) 0%, transparent 70%);
            background-size: 600px 400px, 600px 400px;
            background-position: 0% 0%, 100% 100%;
            border-radius: 40px;
            pointer-events: none;
            opacity: 0.6;
            z-index: 0;
        }

        @keyframes borderGlow {
            0%, 100% {
                background-position: 0% 50%;
                opacity: 0.6;
            }
            50% {
                background-position: 100% 50%;
                opacity: 0.9;
            }
        }


        .header h1 {
            font-size: 3rem;
            color: white;
            text-align: center;
            margin-bottom: 1.5rem;
            font-weight: 950;
            letter-spacing: -0.05em;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            line-height: 1.1;
        }

        @keyframes titleShimmer {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        @keyframes underlineFlow {
            0%, 100% {
                background-position: 0% 50%;
                opacity: 0.8;
            }
            50% {
                background-position: 100% 50%;
                opacity: 1;
            }
        }
        .status-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 2rem;
            flex-wrap: wrap;
            position: relative;
            z-index: 2;
        }
        .status-item {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 32px;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            letter-spacing: 0.02em;
        }
        .status-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }
        .status-item:hover::before {
            left: 100%;
        }
        .status-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3), 0 6px 15px rgba(0, 0, 0, 0.15);
        }
        .providers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 2.5rem;
            margin-top: 3rem;
            align-items: start;
        }
        .provider-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
            border-radius: 28px;
            padding: 2.5rem;
            box-shadow: 0 20px 55px rgba(0, 0, 0, 0.12), 0 12px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
        }
        .provider-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 简洁的边框装饰 */
                linear-gradient(90deg, rgba(102, 126, 234, 0.08) 1px, transparent 1px, transparent calc(100% - 1px), rgba(102, 126, 234, 0.08) calc(100% - 1px)),
                linear-gradient(0deg, rgba(118, 75, 162, 0.08) 1px, transparent 1px, transparent calc(100% - 1px), rgba(118, 75, 162, 0.08) calc(100% - 1px));
            background-size: 100% 100%;
            border-radius: 28px;
            pointer-events: none;
            opacity: 0.5;
        }
        .provider-card::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            background-image:
                /* 内部装饰点 */
                radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 2px, transparent 2px);
            border-radius: 16px;
            pointer-events: none;
            opacity: 0.6;
        }
        .provider-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15), 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        .provider-card.enabled {
            border-color: rgba(52, 211, 153, 0.6);
            box-shadow: 0 15px 45px rgba(52, 211, 153, 0.15), 0 8px 25px rgba(0, 0, 0, 0.06);
        }
        .provider-card.enabled:hover {
            border-color: rgba(52, 211, 153, 0.8);
            box-shadow: 0 25px 60px rgba(52, 211, 153, 0.2), 0 15px 35px rgba(0, 0, 0, 0.08);
        }
        .provider-card.disabled {
            border-color: rgba(248, 113, 113, 0.6);
            opacity: 0.75;
            box-shadow: 0 15px 45px rgba(248, 113, 113, 0.1), 0 8px 25px rgba(0, 0, 0, 0.06);
        }
        .provider-card.error {
            border-color: rgba(251, 191, 36, 0.6);
            box-shadow: 0 15px 45px rgba(251, 191, 36, 0.15), 0 8px 25px rgba(0, 0, 0, 0.06);
        }
        .provider-card.missing-key {
            border-color: rgba(168, 85, 247, 0.6);
            opacity: 0.85;
            box-shadow: 0 15px 45px rgba(168, 85, 247, 0.15), 0 8px 25px rgba(0, 0, 0, 0.06);
        }
        .models-section {
            margin-top: 2rem;
            border-top: 1px solid rgba(226, 232, 240, 0.8);
            padding-top: 1.5rem;
            position: relative;
        }
        .models-toggle {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            color: white;
            border: none;
            padding: 0.875rem 1.5rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.95rem;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
            justify-content: center;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25), 0 3px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        .models-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.35), 0 4px 12px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, rgba(102, 126, 234, 1) 0%, rgba(118, 75, 162, 1) 100%);
        }
        .models-toggle .arrow {
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 1.1rem;
        }
        .models-toggle.expanded .arrow {
            transform: rotate(180deg);
        }
        .models-list {
            display: none;
            margin-top: 1.5rem;
            max-height: 350px;
            overflow-y: auto;
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 16px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.98) 100%);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1), 0 6px 20px rgba(0, 0, 0, 0.06);
            position: relative;
        }
        .models-list::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 简洁的列表装饰 */
                linear-gradient(0deg, transparent 0%, transparent 50%, rgba(102, 126, 234, 0.04) 50%, rgba(102, 126, 234, 0.04) 50.5%, transparent 50.5%, transparent 100%);
            background-size: 100% 60px;
            border-radius: 16px;
            pointer-events: none;
            opacity: 0.3;
        }
        .models-list.expanded {
            display: block;
            animation: slideDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        /* 美化滚动条 */
        .models-list::-webkit-scrollbar {
            width: 8px;
        }
        .models-list::-webkit-scrollbar-track {
            background: rgba(241, 245, 249, 0.5);
            border-radius: 4px;
        }
        .models-list::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 100%);
            border-radius: 4px;
        }
        .models-list::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
        }
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.98);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }
        .models-controls {
            padding: 1rem;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
            border-radius: 16px 16px 0 0;
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
            position: relative;
        }
        .models-controls::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 控制面板装饰线 */
                linear-gradient(90deg, rgba(102, 126, 234, 0.06) 0%, rgba(102, 126, 234, 0.06) 100%);
            background-size: 100% 1px;
            background-position: 0 bottom;
            border-radius: 16px 16px 0 0;
            pointer-events: none;
            opacity: 0.4;
        }
        .models-controls button {
            padding: 0.5rem 1rem;
            border: 1px solid rgba(226, 232, 240, 0.6);
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            color: #374151;
        }
        .models-controls button:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(102, 126, 234, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .models-controls button.selected {
            background: rgba(220, 53, 69, 0.1);
            border-color: rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }
        .models-controls button.selected:hover {
            background: rgba(220, 53, 69, 0.15);
            border-color: rgba(220, 53, 69, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
        }
        .models-controls button.partial {
            background: rgba(255, 193, 7, 0.1);
            border-color: rgba(255, 193, 7, 0.3);
            color: #f59e0b;
        }
        .models-controls button.partial:hover {
            background: rgba(255, 193, 7, 0.15);
            border-color: rgba(255, 193, 7, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
        }
        .copy-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white !important;
            border: none !important;
        }
        .copy-btn:hover {
            background: linear-gradient(135deg, #218838 0%, #1ea080 100%) !important;
        }
        .model-item {
            padding: 0.75rem;
            border-bottom: 1px solid rgba(226, 232, 240, 0.5);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        .model-item:last-child {
            border-bottom: none;
        }
        .model-item:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
            border-radius: 8px;
            margin: 0 4px;
            padding: 0.6rem 0.75rem;
        }
        .model-checkbox {
            margin: 0;
            cursor: pointer;
            width: 18px;
            height: 18px;
            accent-color: #667eea;
            border-radius: 4px;
        }
        .model-name {
            flex: 1;
            font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
            font-size: 0.9rem;
            color: #374151;
            word-break: break-word;
            font-weight: 500;
            letter-spacing: 0.005em;
            cursor: pointer;
            transition: all 0.2s ease;
            line-height: 1.5;
            padding: 0.2rem 0;
            text-rendering: optimizeLegibility;
            font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .model-checkbox:checked + .model-name {
            color: #667eea;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(102, 126, 234, 0.1);
        }
        .model-item:has(.model-checkbox:checked) {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.12) 0%, rgba(118, 75, 162, 0.12) 100%);
            border-left: 3px solid #667eea;
            padding-left: 0.5rem;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }
        /* 模型名字的特殊样式 */
        .model-name::selection {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
        }
        .model-name::-moz-selection {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
        }
        /* 新增模型高亮效果 */
        .model-item.new-model {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(16, 185, 129, 0.15) 100%);
            border-left: 4px solid #22c55e;
            border-radius: 8px;
            margin: 2px 4px;
            padding: 0.6rem 0.75rem;
            animation: newModelPulse 3s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }
        .model-item.new-model::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(16, 185, 129, 0.05) 100%);
            border-radius: 8px;
            pointer-events: none;
        }
        .model-item.new-model .model-name {
            color: #059669;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(34, 197, 94, 0.15);
            position: relative;
            z-index: 1;
        }
        .model-item.new-model .model-checkbox {
            accent-color: #22c55e;
            position: relative;
            z-index: 1;
        }
        @keyframes newModelPulse {
            0%, 100% {
                box-shadow: 0 2px 8px rgba(34, 197, 94, 0.2), 0 0 0 0 rgba(34, 197, 94, 0.4);
            }
            50% {
                box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3), 0 0 0 6px rgba(34, 197, 94, 0.1);
            }
        }
        /* 悬浮时的微妙效果 */
        .model-item:hover .model-name {
            color: #1f2937;
            transform: translateX(2px);
        }
        .model-item:hover .model-checkbox:checked + .model-name {
            color: #5b21b6;
        }
        .provider-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5rem;
        }
        .provider-title {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            flex: 1;
            min-width: 0;
        }
        .provider-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            object-fit: contain;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
            padding: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }
        .provider-icon:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }
        .provider-icon img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 6px;
        }
        .provider-name {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.3;
            word-break: break-word;
            margin-top: 0.1rem;
        }
        .status-badge {
            padding: 0.6rem 1.2rem;
            border-radius: 16px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 0.2rem;
        }
        .status-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
        }
        .status-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
        }
        .status-retrying {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .status-disabled {
            background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
            color: #6c757d;
        }
        .status-missing-key {
            background: linear-gradient(135deg, #e1bee7 0%, #d1a7dd 100%);
            color: #6a1b9a;
        }
        .provider-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(168, 237, 234, 1) 0%, rgba(254, 214, 227, 1) 100%);
            border-radius: 16px;
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1), 0 6px 15px rgba(0, 0, 0, 0.06);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 统计卡片装饰 */
                radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.1) 30%, transparent 30%);
            border-radius: 16px;
            pointer-events: none;
            opacity: 0.4;
        }
        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15), 0 8px 20px rgba(0, 0, 0, 0.08);
        }
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-top: 0.2rem;
        }
        .provider-details {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        .error-message {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            color: #c53030;
            font-size: 0.9rem;
        }
        .error-message code {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 0.2rem 0.4rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            color: #2d3748;
        }
        .footer {
            text-align: center;
            margin-top: 4rem;
            padding: 2.5rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            border-radius: 24px;
            color: #64748b;
            border: 1px solid rgba(226, 232, 240, 0.6);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08), 0 6px 15px rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
        }
        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                /* 页脚简洁装饰 */
                linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, transparent 50%, rgba(118, 75, 162, 0.05) 100%);
            background-size: 100% 2px;
            background-position: 0 top;
            border-radius: 24px;
            pointer-events: none;
            opacity: 0.5;
        }
        .footer p {
            margin: 0.5rem 0;
            font-weight: 500;
        }
        .footer p:first-child {
            font-size: 1.1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .animated-bg {
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }
        /* Toast 通知样式 */
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .providers-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 2rem;
            }
        }
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            .header h1 {
                font-size: 2.5rem;
            }
            .header h1::after {
                width: 80px;
            }
            .status-info {
                flex-direction: column;
                gap: 1rem;
            }
            .status-item {
                padding: 1rem 1.5rem;
                font-size: 0.9rem;
            }
            .providers-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            .provider-card {
                padding: 2rem;
            }
            .models-list {
                max-height: 250px;
            }
        }
        @media (max-width: 480px) {
            .header h1 {
                font-size: 2rem;
            }
            .status-item {
                padding: 0.8rem 1.2rem;
                font-size: 0.85rem;
            }
            .provider-card {
                padding: 1.5rem;
            }
            .models-controls {
                padding: 0.8rem;
                gap: 0.5rem;
            }
            .models-controls button {
                padding: 0.4rem 0.8rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body class="{{backgroundClass}}">
    <div class="overlay">
        <div class="container">
            <div class="header">
                <h1>{{title}}</h1>
                <div class="status-info">
                    <div class="status-item">
                        总提供商: {{totalProviders}}
                    </div>
                    <div class="status-item">
                        已启用: {{enabledProviders}}
                    </div>
                </div>
            </div>
            <div class="providers-grid">
                {{#providers}}
                <div class="provider-card {{#enabled}}enabled{{/enabled}}{{^enabled}}{{#isMissingKey}}missing-key{{/isMissingKey}}{{^isMissingKey}}disabled{{/isMissingKey}}{{/enabled}}{{#error}} error{{/error}}">
                    <div class="provider-header">
                        <div class="provider-title">
                            {{#iconHtml}}
                                {{{iconHtml}}}
                            {{/iconHtml}}
                            <div class="provider-name">{{name}}</div>
                        </div>
                        <div class="status-badge {{#enabled}}{{#isRetrying}}status-retrying{{/isRetrying}}{{^isRetrying}}{{#error}}status-error{{/error}}{{^error}}status-success{{/error}}{{/isRetrying}}{{/enabled}}{{^enabled}}{{#isMissingKey}}status-missing-key{{/isMissingKey}}{{^isMissingKey}}status-disabled{{/isMissingKey}}{{/enabled}}">
                            {{#enabled}}
                                {{#isRetrying}}重试中{{/isRetrying}}
                                {{^isRetrying}}
                                    {{#error}}错误{{/error}}
                                    {{^error}}正常{{/error}}
                                {{/isRetrying}}
                            {{/enabled}}
                            {{^enabled}}
                                {{#isMissingKey}}缺少密钥{{/isMissingKey}}
                                {{^isMissingKey}}已禁用{{/isMissingKey}}
                            {{/enabled}}
                        </div>
                    </div>
                    <div class="provider-stats">
                        <div class="stat-item">
                            <div class="stat-value">{{modelCount}}</div>
                            <div class="stat-label">模型数量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">{{#lastSuccess}}✓{{/lastSuccess}}{{^lastSuccess}}✗{{/lastSuccess}}</div>
                            <div class="stat-label">状态</div>
                        </div>
                    </div>
                    <div class="provider-details">
                        {{#lastCheck}}
                            <div>最后检查: {{lastCheck}}</div>
                        {{/lastCheck}}
                        {{#lastSuccess}}
                            <div>最后成功: {{lastSuccess}}</div>
                        {{/lastSuccess}}
                        {{#retryCount}}
                            <div style="color: #856404;">重试次数: {{retryCount}}/2</div>
                        {{/retryCount}}
                        {{#nextRetryAt}}
                            <div style="color: #856404;">下次重试: {{nextRetryAt}}</div>
                        {{/nextRetryAt}}
                    </div>
                    {{#error}}
                    <div class="error-message">
                        {{#isMissingKey}}
                            <strong>缺少 API 密钥</strong><br>
                            请设置环境变量: <code>{{requiredEnvVar}}</code>
                        {{/isMissingKey}}
                        {{^isMissingKey}}
                            {{error}}
                        {{/isMissingKey}}
                    </div>
                    {{/error}}
                    {{#enabled}}
                    {{#modelCount}}
                    <div class="models-section">
                        <button class="models-toggle" onclick="toggleModels('{{id}}')">
                            <span>查看模型列表 ({{modelCount}})</span>
                            <span class="arrow">▼</span>
                        </button>
                        <div class="models-list" id="models-{{id}}">
                            <div class="models-controls">
                                <button id="toggle-{{id}}" onclick="toggleAllModels('{{id}}')">全选</button>
                                <button class="copy-btn" onclick="copySelectedModels('{{id}}')">复制选中</button>
                            </div>
                            <div class="models-container">
                                {{#models}}
                                <div class="model-item{{#isNew}} new-model{{/isNew}}">
                                    <input type="checkbox" class="model-checkbox" id="model-{{id}}-{{name}}" data-provider="{{id}}" data-model="{{name}}">
                                    <label for="model-{{id}}-{{name}}" class="model-name" data-model="{{name}}">{{name}}</label>
                                </div>
                                {{/models}}
                            </div>
                        </div>
                    </div>
                    {{/modelCount}}
                    {{^modelCount}}
                    <div class="models-section">
                        <div style="text-align: center; color: #666; font-style: italic; padding: 1rem;">
                            暂无可用模型
                        </div>
                    </div>
                    {{/modelCount}}
                    {{/enabled}}
                </div>
                {{/providers}}
            </div>
            <div class="footer">
                <p>ModelSentry v1.0 - AI 模型监控工具</p>
            </div>
        </div>
    </div>
    <script>
        // 页面加载完成后启动动画
        document.addEventListener('DOMContentLoaded', () => {
            // 添加页面加载动画
            const cards = document.querySelectorAll('.provider-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // 对所有模型列表按名字排序
            sortAllModelLists();
        });
        // 对所有模型列表按名字排序
        function sortAllModelLists() {
            const modelsContainers = document.querySelectorAll('.models-container');
            modelsContainers.forEach(container => {
                const modelItems = Array.from(container.querySelectorAll('.model-item'));

                // 按模型名字排序（不区分大小写）
                modelItems.sort((a, b) => {
                    const nameA = a.querySelector('.model-name').textContent.toLowerCase();
                    const nameB = b.querySelector('.model-name').textContent.toLowerCase();
                    return nameA.localeCompare(nameB);
                });

                // 重新排列DOM元素
                modelItems.forEach(item => {
                    container.appendChild(item);
                });
            });
        }

        function toggleModels(providerId) {
            const toggle = document.querySelector(`[onclick="toggleModels('${providerId}')"]`);
            const modelsList = document.getElementById(`models-${providerId}`);
            if (modelsList.classList.contains('expanded')) {
                modelsList.classList.remove('expanded');
                toggle.classList.remove('expanded');
            } else {
                modelsList.classList.add('expanded');
                toggle.classList.add('expanded');
            }
        }
        function toggleAllModels(providerId) {
            const checkboxes = document.querySelectorAll(`#models-${providerId} .model-checkbox`);
            const toggleButton = document.getElementById(`toggle-${providerId}`);
            // 检查当前选中状态
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            // 如果全部选中或大部分选中，则取消全选；否则全选
            const shouldSelectAll = checkedCount < totalCount / 2;
            checkboxes.forEach(checkbox => {
                checkbox.checked = shouldSelectAll;
            });
            // 更新按钮文本
            updateToggleButtonText(providerId);
        }
        function updateToggleButtonText(providerId) {
            const checkboxes = document.querySelectorAll(`#models-${providerId} .model-checkbox`);
            const toggleButton = document.getElementById(`toggle-${providerId}`);
            if (!toggleButton) return;
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            if (checkedCount === 0) {
                toggleButton.textContent = '全选';
                toggleButton.className = '';
            } else if (checkedCount === totalCount) {
                toggleButton.textContent = '取消全选';
                toggleButton.className = 'selected';
            } else {
                toggleButton.textContent = `全选 (${checkedCount}/${totalCount})`;
                toggleButton.className = 'partial';
            }
        }
        function copySelectedModels(providerId) {
            const checkboxes = document.querySelectorAll(`#models-${providerId} .model-checkbox:checked`);
            const selectedModels = Array.from(checkboxes).map(checkbox => checkbox.dataset.model);
            if (selectedModels.length === 0) {
                alert('请先选择要复制的模型');
                return;
            }
            const separator = '{{modelCopySeparator}}';
            const modelsText = selectedModels.join(separator);
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(modelsText).then(() => {
                    showCopySuccess(selectedModels.length);
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopy(modelsText);
                });
            } else {
                fallbackCopy(modelsText);
            }
        }
        function fallbackCopy(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
                const separator = '{{modelCopySeparator}}';
                const count = text.split(separator).length;
                showCopySuccess(count);
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            } finally {
                document.body.removeChild(textArea);
            }
        }
        function showCopySuccess(count) {
            const toast = document.createElement('div');
            toast.textContent = `已复制 ${count} 个模型名称到剪贴板`;
            toast.style.cssText = `
                position: fixed; top: 20px; right: 20px; background: #28a745; color: white;
                padding: 12px 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000; font-size: 14px; animation: slideIn 0.3s ease;
            `;
            document.body.appendChild(toast);
            setTimeout(() => {
                toast.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
        // 初始化页面时设置事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有复选框添加变化监听器
            document.addEventListener('change', function(event) {
                if (event.target.classList.contains('model-checkbox')) {
                    // 找到对应的提供商ID
                    const modelsList = event.target.closest('.models-list');
                    if (modelsList) {
                        const providerId = modelsList.id.replace('models-', '');
                        updateToggleButtonText(providerId);
                    }
                }
            });
            // 初始化所有切换按钮的文本
            document.querySelectorAll('.models-list').forEach(modelsList => {
                const providerId = modelsList.id.replace('models-', '');
                updateToggleButtonText(providerId);
            });
        });
    </script>
</body>
</html>
